import React, { useRef } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { LazyLoad } from "Components/LazyLoad";
import { MkdListTableV2 } from "Components/MkdListTable";
import "./AdminListReferralPage.css";
import { AiFillEye } from "react-icons/ai";
import { EditIcon2, TrashIcon } from "Assets/svgs";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Plan Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Description",
    accessor: "description",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Truncate description if it's too long
      if (value && value.length > 50) {
        return value.substring(0, 50) + "...";
      }
      return value;
    }
  },
  {
    header: "Amount",
    accessor: "price",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value, row) => {
      // Format price with billing cycle
      const price = parseFloat(value).toFixed(2);
      const cycle = row.billing_cycle || "monthly";
      return `$${price}/${cycle.replace('ly', '')}`;
    }
  },
  {
    header: "Created At",
    accessor: "created_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Format date as YYYY-MM-DD
      const date = new Date(value);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  },
  {
    header: "Status",
    accessor: "is_active",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Show Active status with green background
      return value === 1 ?
        `<span class="status active">Active</span>` :
        `<span class="status deleted">Inactive</span>`;
    }
  },

];

const AdminListPlansPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [searchValue, setSearchValue] = React.useState("");
  const refreshRef = useRef(null);

  // Function to handle view action
  const handleView = (ids) => {
    console.log("View plan with ID:", ids[0]);
    navigate(`/admin/view-plan/${ids[0]}`);
  };

  // Function to handle edit action
  const handleEdit = (ids) => {
    console.log("Edit plan with ID:", ids[0]);
    // Implement edit functionality here
  };

  // Function to handle delete action
  const handleDelete = async (ids) => {
    try {
      console.log("Deleting plan with ID:", ids[0]);
      const sdk = new MkdSDK();
      sdk.setTable("plan");
      const result = await sdk.callRestAPI({ id: ids[0] }, "DELETE");

      console.log("Delete result:", result);

      // Refresh the table data
      if (refreshRef.current) {
        refreshRef.current.click();
      }
    } catch (error) {
      console.error("Error deleting plan:", error);
    }
  };

  // Sample plan data based on the provided API response format
  const samplePlans = [
    {
      id: 1,
      name: "Basic Plan",
      description: "Essential features for starters",
      price: "29.00",
      billing_cycle: "monthly",
      features: "[]",
      is_popular: 0,
      is_trial_available: 0,
      trial_days: null,
      is_active: 1,
      stripe_product_id: "prod_SBtWL2UUf6XHVc",
      stripe_price_id: "price_1RHVjOBgOlWo0lDU1DvRplWK",
      created_at: "2025-01-15T19:46:46.000Z",
      updated_at: "2025-01-15T19:46:46.000Z"
    },
    {
      id: 2,
      name: "Premium Plan",
      description: "Advanced features for professionals",
      price: "99.00",
      billing_cycle: "monthly",
      features: "[]",
      is_popular: 1,
      is_trial_available: 0,
      trial_days: null,
      is_active: 1,
      stripe_product_id: "prod_SBtWL2UUf6XHVc",
      stripe_price_id: "price_1RHVjOBgOlWo0lDU1DvRplWK",
      created_at: "2025-01-10T19:46:46.000Z",
      updated_at: "2025-01-10T19:46:46.000Z"
    },
    {
      id: 3,
      name: "Enterprise Plan",
      description: "Complete solution for large teams",
      price: "299.00",
      billing_cycle: "monthly",
      features: "[]",
      is_popular: 0,
      is_trial_available: 0,
      trial_days: null,
      is_active: 1,
      stripe_product_id: "prod_SBtWL2UUf6XHVc",
      stripe_price_id: "price_1RHVjOBgOlWo0lDU1DvRplWK",
      created_at: "2025-01-05T19:46:46.000Z",
      updated_at: "2025-01-05T19:46:46.000Z"
    }
  ];

  return (
    <>
      <div className="opportunities-dashboard bg-[#1E1E1E]">
        <div className="container">
          {/* Header */}
          <div className="header">
            <h1>Plans</h1>
            <p>Manage and configure subscription plans</p>
          </div>

          {/* Search and Add Button */}
          <div className="search-add">
            <div className="search-container">
              <div className="search-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search plans..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          {/* Table */}
          <div className="table-container">
            <LazyLoad>
              <MkdListTableV2
                columns={columns}
                tableRole={"admin"}
                table={"plan"}
                actionId={"id"}
                actions={{
                  view: {
                    show: true,
                    action: handleView,
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "View",
                    icon: <AiFillEye className="text-blue-500" />
                  },
                  edit: {
                    show: false,
                    multiple: false,
                    action: handleEdit,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Edit",
                    icon: <EditIcon2 stroke="#4CAF50" />
                  },
                  delete: {
                    show: false,
                    action: handleDelete,
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Delete",
                    icon: <TrashIcon fill="#E53E3E" />
                  },
                  select: { show: false, action: null, multiple: false },
                  add: { show: false, action: null, multiple: false },
                  export: { show: false, action: null, multiple: false },
                }}
                actionPosition={["buttons"]}
                refreshRef={refreshRef}
                externalData={{
                  use: true,
                  data: samplePlans,
                  loading: false,
                  page: 1,
                  limit: 10,
                  pages: 1,
                  total: 3
                }}
              />
            </LazyLoad>
          </div>

          {/* Pagination */}
          <div className="pagination">
            <div className="pagination-info">Showing 1 to 3 of 3 entries</div>
            <div className="pagination-buttons">
              <button className="pagination-button prev">Previous</button>
              <button className="pagination-button next">Next</button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminListPlansPage;
