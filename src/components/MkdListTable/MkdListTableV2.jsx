import React, { memo, useCallback, useEffect, useMemo, useRef } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { StringCaser, generateUUID, getNonNullValue } from "Utils/utils";
import { PaginationBar } from "Components/PaginationBar";
import { AuthContext, tokenExpireError } from "Context/Auth";
import {
  GlobalContext,
  createRequest,
  customRequest,
  getMany,
  setLoading as setGlobalLoading,
} from "Context/Global";
import {
  MkdListTable,
  MkdListTableFilter,
  OverlayTableActions,
  TableActions,
} from "Components/MkdListTable";
import { AddButton } from "Components/AddButton";
import { ExportButton } from "Components/ExportButton";
import { LazyLoad } from "Components/LazyLoad";
import TreeSDK from "Utils/TreeSDK";
import "./MkdListTable.css";
import { ExCircleIcon } from "Assets/svgs";
import { useProfile } from "Hooks/useProfile";
import { BiSearch } from "react-icons/bi";
import { AiOutlineClose } from "react-icons/ai";
import { MkdInput } from "Components/MkdInput";
import { operationActions, operations } from "Utils/config";
import { getProcessedTableData } from "./MkdListTableRowListColumn";

const getCorrectValueTypeFormat = (value, operator) => {
  // console.log("typeof value", typeof value);
  if (typeof value === "string") {
    return ["eq"].includes(operator) ? `'${value}'` : value;
  }
  // if (typeof value === "number") {
  //   return ["cs"].includes(operator) ? `'${value}'` : value;
  // }
  return value;
};
const getCorrectOperator = (operator, value) => {
  if (typeof value === "number") {
    return ["cs"].includes(operator) ? "eq" : operator;
  }
  return operator;
};
/**
 * @typedef {Object} ActionItem
 * @property {boolean} show Whether to show the action button
 * @property {Function} [action] Function to execute when the action is triggered, receives an array of selected IDs
 * @property {boolean} [multiple] Whether the action supports multiple selections
 * @property {"static" | "toggle"} [type] static or toggle
 * @property {import("react").ReactNode} [children] The content to display for the action button
 * @property {boolean} [showChildren] Whether to show the children content
 * @property {boolean} [className] Additional class name to apply to the action button
 * @property {Array<"dropdown" | "ontop" | "overlay" | "buttons">} locations Locations where the action should be displayed
 * @property {import("react").ReactNode} icon The icon to display for the action button
 * @property {Object} bind Additional configuration for action binding
 * @property {string | Array<string>} bind.column Columns to apply the binding action on
 * @property {keyof typeof operationActions} bind.action Action to bind to the columns (e.g., "hide" or "disable")
 * @property {keyof typeof operations | Array<keyof typeof operations>} bind.operator Operators for the binding logic (e.g., "EQUAL", "NOT_EQUAL")
 * @property {"or" | "and"} [bind.logic] Logic operator (e.g., "or", "and") to apply between conditions
 * @property {string | number | null | any | Array<number|string|null|any>} bind.ifValue Values to compare the column against when applying the bind action
 */
// operationActions

/**
 * @typedef {Object} ActionsProp
 * @property {ActionItem} [add] Action item configuration for the "add" action (optional)
 * @property {ActionItem} [edit] Action item configuration for the "edit" action (optional)
 * @property {ActionItem} [view] Action item configuration for the "view" action (optional)
 * @property {ActionItem} [select] Action item configuration for the "select" action (optional)
 * @property {ActionItem} [delete] Action item configuration for the "delete" action (optional)
 * @property {ActionItem} [export] Action item configuration for the "export" action (optional)
 * @property {ActionItem} [custom_action] Action item configuration for the "custom_action" action (optional)
 */

/**
 * @typedef {Object} ExternalData
 * @property {boolean} [use=false] Whether to use external data @default false
 * @property {Array} [data=[]] Data to use @default []
 * @property {boolean} [loading=false] Whether the data is loading @default false
 * @property {Function} [fetch=(page, limit, filter)=> {}] Function to fetch data @default () => {}
 * @property {Function} [search=(search, query)=> {}] Function to search data @default () => {}
 * @property {number} [limit=0] Limit to use @default 0
 * @property {number} [pages=0] Pages to use @default 0
 * @property {number} [page=1] Page to use @default 1
 * @property {number} [total=0] Total to use @default 0
 */

/**
 * @function
 * @description Component for rendering a table with data from a provided API.
 * @param {Object} props Component props
 * @param {ExternalData} [props.externalData] External data configuration
 * @param {Array} [props.defaultColumns=[]] Default columns to display in the table
 * @param {Array} [props.processes=[]] Array of Functions to run on data
 * @param {string} [props.columnModel=""] model to fetch columns to display, table prop value is used if not provided
 * @param {string} [props.table=""] Table name to query in the API
 * @param {string} [props.tableTitle=""] Title to display at the top of the table
 * @param {Array} [props.schemaFields=[]] Fields to include in the API query
 * @param {boolean} [props.hasFilter=true] Whether to include a filter input above the table
 * @param {boolean} [props.allowEditing=false] Whether to allow editing of table cells
 * @param {boolean} [props.allowSortColumns=true] Whether to allow sorting of columns
 * @param {string} [props.topClasses=""] Additional classes to apply to the top container element
 * @param {ActionsProp} [props.actions] Object containing action buttons to display above the table
 * @param {"dropwdown" | "ontop" | "overlay" | "buttons"} [props.actionPostion=["dropdown"]] Position of the action buttons
 * @param {string} [props.tableRole="admin"] Role to use when querying the API
 * @param {Array} [props.join=[]] Array containing join information to pass to the API
 * @param {Function} [props.refreshRef=null] Reference to a button element to refresh the table data
 * @param {boolean} [props.showSearch=true] Whether to show the search input
 * @param {boolean} [props.showPagination=true] Whether to show the pagination
 * @param {boolean} [props.maxHeight=""] Max height of the table if not in a fixed height container
 * @param {Function} [props.onReady=null] Function to call when the table data is ready
 * @param {number} [props.defaultPageSize] Default page size for the table
 * @param {Array} [props.defaultFilter=[]] Array of default filters to apply to the table
 * @param {Array} [props.searchFilter=[]] Array of search filters to apply to the table
 * @param {Array} [props.filterDisplays=[]] Array of display types for the filters
 * @returns {import("react").ReactElement} A React element containing the table
 */

const MkdListTableV2 = ({
  // columnId,
  pageUser = false,
  columns = [], // Accept columns prop for backward compatibility
  useDefaultColumns = false,
  defaultColumns = [],
  excludeColumns = [],
  columnModel = null,
  // setColumns,
  processes = [],
  searchInitialProcesses = [],
  actions = {
    view: { show: true, multiple: true, action: null },
    edit: { show: true, multiple: true, action: null },
    delete: { show: true, multiple: true, action: null },
    select: { show: true, multiple: true, action: null },
    action: {
      show: false,
      multiple: false,
      action: null,
      showChildren: true,
      children: "+ Add",
      type: "",
      className: "",
      locations: [],
      icon: null,
    },
    add: {
      show: true,
      multiple: true,
      action: null,
      showChildren: true,
      children: "+ Add",
      type: "",
      className: "",
    },
    export: {
      show: true,
      multiple: true,
      action: null,
      showText: false,
      className: "",
    },
  },
  updateRef = null,
  onUpdateCurrentTableData = null,
  actionPostion = ["dropdown"], // "dropwdown" | "ontop" | "overlay" | "buttons"
  actionPosition, // Accept actionPosition prop for backward compatibility
  actionId = "id",
  tableRole = "admin",
  table = "user",
  tableTitle = "",
  tableSchema = [],
  hasFilter = true,
  schemaFields = [],
  showPagination = true,
  defaultFilter = [],
  refreshRef = null,
  allowEditing = false,
  allowSortColumns = true,
  showSearch = true,
  topClasses = "",
  join = [],
  filterDisplays = [],
  resetFilters = null,
  defaultPageSize = 500,
  searchFilter = [],
  onReady = null,
  maxHeight = null,
  rawFilter = [],
  externalData = {
    page: 1,
    data: [],
    limit: 0,
    pages: 0,
    total: 0,
    use: false,
    loading: false,
    canNextPage: false,
    canPreviousPage: false,
    fetch: (page, limit, filter) => {},
    search: (search, columns, searchFilter, query) => {},
  },
  canChangeLimit = true,
  selectedItemsRef = null,
}) => {
  const tdk = useMemo(() => new TreeSDK(), []);
  const sdk = useMemo(() => new MkdSDK(), []);
  const abortControllerRef = useRef(null);

  const { dispatch } = React.useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: { columModel },
  } = React.useContext(GlobalContext);

  const [query, setQuery] = React.useState("");
  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(defaultPageSize);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(1);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deleteLoading, setDeleteLoading] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [selectedItems, setSelectedItems] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [isSearchDirty, setIsSearchDirty] = React.useState(false);
  // const [optionValue, setOptionValue] = React.useState("eq");
  const [isLoading, setIsLoading] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [runFilter, setRunFilter] = React.useState(false);
  const [searchField, setSearchField] = React.useState("name");

  const [columnData, setColumnData] = React.useState({
    views: [],
    data: null,
    columns: [],
    columnId: 0,
    columnsReady: false,
  });

  const [popoverShown, setPopoverShow] = React.useState(false);

  // const { profile, getProfile } = useProfile();
  const profile = { id: 22 };
  console.log("somethinng", profile);

  const selectedOptionsMemo = useMemo(() => selectedOptions, [selectedOptions]);
  const currentTableDataMemo = useMemo(
    () => JSON.stringify(currentTableData),
    [currentTableData]
  );

  const schema = yup.object({});

  const {
    register,
    handleSubmit,
    setError,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const processFilters = useCallback(() => {
    let filters = [];
    const uniqueSet = new Set(
      selectedOptionsMemo.map((item) => item?.accessor)
    );

    uniqueSet.forEach((uniqueSetItem) => {
      const filterSet = selectedOptionsMemo.filter(
        (item) => item.accessor === uniqueSetItem
      );

      if (filterSet?.length > 0) {
        const valueSet = filterSet.filter((item) => item?.value);

        if (valueSet.length > 1) {
          valueSet.forEach((valueSetItem) => {
            const { accessor, operator, value } = valueSetItem;
            const filter = `Dealmaker_${table}.${accessor},${
              operator === "cs" || operator === "eq"
                ? getCorrectOperator("o" + operator, value)
                : getCorrectOperator(operator, value)
            },${value}`;
            filters.push(filter);
          });
        } else if (valueSet.length === 1) {
          const { accessor, operator, value } = valueSet[0];
          filters.push(
            `Dealmaker_${table}.${accessor},${getCorrectOperator(
              operator,
              value
            )},${getCorrectValueTypeFormat(value, operator)}`
          );
        }
      }
    });
    return filters;
  }, [selectedOptionsMemo, table]);

  const getSearchData = useCallback(
    async (query = { limit: pageSize, page: 1 }) => {
      const treeFilter = processFilters();
      let columns = columnData?.columns;

      if (searchInitialProcesses.length > 0) {
        for (const initialProcess of searchInitialProcesses) {
          columns = initialProcess(columns, treeFilter);
        }
      }
      console.log("treeFilter >>", treeFilter, columns);

      try {
        const apiEndpoint = `/v3/api/custom/Dealmaker/generic/search/${table}?limit=${query?.limit}&page=${query?.page}`;
        setLoading(true);
        const result = await customRequest(
          globalDispatch,
          dispatch,
          {
            endpoint: apiEndpoint,
            method: "POST",
            payload: {
              search: searchValue,
              columns: columns,
              filter: searchFilter,
              tree_filter: treeFilter,
            },
          },
          "tableSearchData",
          false
        );
        if (!result?.error) {
          setSelectedItems([]);
          const { data, total, limit, num_pages, page } = result;

          let list = data;

          if (processes?.length) {
            for (const eachProcess of processes) {
              // if type of process is a function
              if (["function"].includes(typeof eachProcess)) {
                list = eachProcess(list, columnData?.columns);
              }
            }
          }

          const processedTableData = await getProcessedTableData(
            list,
            columnData?.columns,
            globalDispatch,
            dispatch
          );
          // console.log("processedTableData >>", processedTableData);
          setCurrentTableData(() => processedTableData);
          setPageSize(Number(limit));
          setPageCount(num_pages ?? pageCount);
          setPage(Number(page));
          setDataTotal(Number(total));
          setCanPreviousPage(Number(page) > 1);
          setCanNextPage(
            Number(page) + 1 <= num_pages ? Number(num_pages) : pageCount
          );
          setLoading(false);
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
      // finally {
      //   setColumnsReady(false);
      // }
    },
    [
      table,
      pageSize,
      searchValue,
      columnData,
      searchFilter,
      globalDispatch,
      dispatch,
      pageCount,
      selectedOptionsMemo,
    ]
  );

  const getData = useCallback(
    async (pageNum, limitNum, currentTableData) => {
      console.log("getData called with:", { pageNum, limitNum, filterConditions, table });
      try {
        setLoading(true);
        const result = await tdk.getPaginate("super_admin", table, {
          size: limitNum || 500,
          page: pageNum || 1,
          ...{
            ...(filterConditions.length
              ? {
                  filter: [
                    ...(defaultFilter.length ? defaultFilter : []),
                    ...filterConditions,
                  ],
                }
              : defaultFilter.length
              ? { filter: [...defaultFilter] }
              : null),
          },
        });

        const { list, total, limit, num_pages, page } = result;

        setCurrentTableData(list);
        setPageSize(Number(limit));
        setPageCount(num_pages ?? pageCount);
        setPage(Number(page));
        setDataTotal(Number(total));
        setCanPreviousPage(Number(page) > 1);
        setCanNextPage(
          Number(page) + 1 <= num_pages ? Number(num_pages) : pageCount
        );
        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error("ERROR fetching data:", error);
      }
      // finally {
      //   setColumnsReady(false);
      // }
    },
    [
      table,
      filterConditions,
      defaultFilter,
      pageCount,
    ]
  );

  const onSort = useCallback(
    (columnIndex) => {
      if (columnData?.columns[columnIndex].isSorted) {
        columnData.columns[columnIndex].isSortedDesc =
          !columnData?.columns[columnIndex].isSortedDesc;
      } else {
        columnData?.columns.forEach((col) => {
          col.isSorted = false;
          col.isSortedDesc = false;
        });
        columnData.columns[columnIndex].isSorted = true;
      }

      (async function () {
        if (!searchValue) {
          if (externalData?.use) {
            setLoading(true);
            externalData?.fetch(currentPage, pageSize, {
              filterConditions: [],
              order: columnData?.columns[columnIndex].accessor,
              direction: columnData?.columns[columnIndex].isSortedDesc
                ? "desc"
                : "asc",
            });
          } else {
            await getData(currentPage, pageSize, {
              filterConditions: [],
              order: columnData?.columns[columnIndex].accessor,
              direction: columnData?.columns[columnIndex].isSortedDesc
                ? "desc"
                : "asc",
            });
          }
        } else if (searchValue) {
          getSearchData({
            limit: pageSize,
            page: currentPage,
          });
        }
      })();
    },
    [columnData, currentPage, pageSize, filterConditions, getData]
  );

  const updatePageSize = useCallback(
    (limit) => {
      (async function () {
        setPageSize(limit);

        if (!searchValue) {
          await getData(currentPage, limit, {
            filterConditions: [],
          });
          setIsSearchDirty(false);
        } else if (searchValue) {
          getSearchData({ limit, page: currentPage });
        }
      })();
    },
    [isSearchDirty, searchValue, currentPage, getData, getSearchData]
  );

  const onColumnClick = useCallback((column) => {
    const data = {
      uid: generateUUID(),
      accessor: column,
      operator: "cs",
      value: "",
    };

    setSelectedOptions((prev) => [...prev, data]);
  }, []);

  const setOptionValue = useCallback((field, value, uid) => {
    setSelectedOptions((prev) =>
      prev.map((item) =>
        item?.uid === uid ? { ...item, [field]: value } : item
      )
    );

    if (field === "value") {
      setRunFilter(true);
    }
  }, []);

  const previousPage = useCallback(() => {
    (async function () {
      if (!searchValue) {
        if (externalData?.use) {
          setLoading(true);
          externalData?.fetch(
            currentPage - 1 > 0 ? currentPage - 1 : currentPage,
            pageSize
          );
        } else {
          await getData(
            currentPage - 1 > 0 ? currentPage - 1 : currentPage,
            pageSize,
            {
              filterConditions: [],
            }
          );
        }
        setIsSearchDirty(false);
      } else if (searchValue) {
        if (externalData?.use) {
          externalData?.search(searchValue, columns, searchFilter, {
            limit: pageSize,
            page: currentPage - 1 > 0 ? currentPage - 1 : currentPage,
          });
        } else {
          getSearchData({
            limit: pageSize,
            page: currentPage - 1 > 0 ? currentPage - 1 : currentPage,
          });
        }
      }
    })();
  }, [
    isSearchDirty,
    searchValue,
    currentPage,
    pageSize,
    getData,
    getSearchData,
  ]);

  const updateCurrentPage = useCallback(
    (page) => {
      (async function () {
        setPage(page);
        if (!searchValue) {
          if (externalData?.use) {
            setLoading(true);
            externalData?.fetch(page, pageSize);
          } else {
            await getData(page, pageSize, {
              filterConditions: [],
            });
          }
          setIsSearchDirty(false);
        } else if (searchValue) {
          if (externalData?.use) {
            externalData?.search(searchValue, columns, searchFilter, {
              limit: pageSize,
              page,
            });
          } else {
            getSearchData({ limit: pageSize, page });
          }
        }
      })();
    },
    [isSearchDirty, searchValue, pageSize, getData, getSearchData]
  );

  const nextPage = useCallback(() => {
    (async function () {
      if (!searchValue) {
        if (externalData?.use) {
          setLoading(true);
          externalData?.fetch(
            currentPage + 1 <= pageCount ? currentPage + 1 : currentPage,
            pageSize
          );
        } else {
          await getData(
            currentPage + 1 <= pageCount ? currentPage + 1 : currentPage,
            pageSize,
            {
              filterConditions: [],
            }
          );
        }
        setIsSearchDirty(false);
      } else if (searchValue) {
        if (externalData?.use) {
          externalData?.search(searchValue, columns, searchFilter, {
            limit: pageSize,
            page: currentPage + 1 <= pageCount ? currentPage + 1 : currentPage,
          });
        } else {
          getSearchData({
            limit: pageSize,
            page: currentPage + 1 <= pageCount ? currentPage + 1 : currentPage,
          });
        }
      }
    })();
  }, [
    isSearchDirty,
    searchValue,
    currentPage,
    pageCount,
    pageSize,
    getData,
    getSearchData,
  ]);

  const addFilterCondition = useCallback(
    (option, selectedValue, inputValue) => {
      // Clear filters if input is empty
      if (!inputValue || inputValue.trim() === "") {
        setFilterConditions((prevConditions) =>
          prevConditions.filter((condition) => !condition.includes(option))
        );
        setSearchValue("");
        return;
      }

      const input =
        selectedValue === "eq" && isNaN(inputValue)
          ? `${inputValue}`
          : inputValue;
      const condition = `${option},${selectedValue},${input}`.toLowerCase();
      setFilterConditions((prevConditions) => {
        const newConditions = prevConditions.filter(
          (condition) => !condition.includes(option)
        );
        return [...newConditions, condition];
      });
      setSearchValue(inputValue);
    },
    []
  );

  const deleteItem = useCallback(
    async (id) => {
      const deleteId = async (idToDelete) => {
        try {
          setDeleteLoading(true);
          sdk.setTable(table);
          const result = await sdk.callRestAPI({ id: idToDelete }, "DELETE");
          if (!result?.error) {
            setCurrentTableData((list) =>
              list.filter((x) => Number(x.id) !== Number(idToDelete))
            );
            setDeleteLoading(false);
            setShowDeleteModal(false);
          }
        } catch (err) {
          setDeleteLoading(false);
          setShowDeleteModal(false);
          tokenExpireError(dispatch, err?.message);
          throw new Error(err);
        }
      };

      if (Array.isArray(id)) {
        for (const idToDelete of id) {
          await deleteId(idToDelete);
        }
      } else if (typeof id === "number") {
        await deleteId(id);
      }
    },
    [table, dispatch]
  );

  const exportTable = useCallback(async () => {
    try {
      sdk.setTable(table);
      const payload = {
        search: getNonNullValue(searchValue),
        columns: columnData?.columns,
        exclude_columns: excludeColumns,
        filter: searchFilter,
        raw_filter: rawFilter,
      };
      await sdk.customExportCSV(payload);
    } catch (err) {
      throw new Error(err);
    }
  }, [table, searchValue, columnData, excludeColumns, searchFilter]);

  const handleAlphaSearchInput = useCallback(
    async (e) => {
      e?.preventDefault();
      if ([e?.code?.toLowerCase(), e?.key?.toLowerCase()].includes("enter")) {
        if (!searchValue) {
          if (externalData?.use) {
            setLoading(true);
            externalData?.fetch(currentPage, pageSize, {
              filterConditions: [],
            });
          } else {
            await getData(currentPage, pageSize, {
              filterConditions: [],
            });
          }
          setIsSearchDirty(false);
        } else if (searchValue) {
          if (externalData?.use) {
            externalData?.search(searchValue, columns, searchFilter);
          } else {
            getSearchData({
              limit: pageSize,
              page: currentPage,
            });
          }
        }
      } else {
        setSearchValue(e?.target?.value);
        if (!isSearchDirty) {
          setIsSearchDirty(true);
        }
      }
    },
    [isSearchDirty, searchValue, currentPage, pageSize, getData, getSearchData]
  );

  const resetForm = useCallback(async () => {
    reset();
    await getData(currentPage, pageSize);
  }, [reset, getData, currentPage, pageSize]);

  const onSubmit = useCallback(() => {
    if (!searchValue) {
      if (externalData?.use) {
        setLoading(true);
        externalData?.fetch(currentPage, pageSize, {
          filterConditions: [],
        });
      } else {
        getData(currentPage, pageSize, {
          filterConditions: [],
        });
      }
    } else if (searchValue) {
      if (externalData?.use) {
        setLoading(true);
        externalData?.search(currentPage, pageSize, {
          filterConditions: [],
        });
      } else {
        getSearchData({
          limit: pageSize,
          page: currentPage,
        });
      }
    }
  }, [selectedOptionsMemo, table, getData, currentPage, pageSize]);

  const updateTableData = useCallback(
    async (id, key, updatedData) => {
      try {
        sdk.setTable(table);
        await sdk.callRestAPI(
          {
            id,
            [key]: updatedData,
          },
          "PUT"
        );
      } catch (error) {
        console.log("ERROR", error);
        tokenExpireError(dispatch, error.message);
      }
    },
    [table, dispatch]
  );

  const handleTableCellChange = useCallback(
    async (id, newValue, index, newValueKey) => {
      let runApiCall;
      newValue = isNaN(Number.parseInt(newValue))
        ? newValue
        : Number.parseInt(newValue);
      try {
        clearTimeout(runApiCall);
        runApiCall = setTimeout(async () => {
          await updateTableData(id, newValueKey, newValue);
        }, 200);
        setCurrentTableData((prevData) =>
          prevData.map((item, i) =>
            i === index ? { ...item, [newValueKey]: newValue } : item
          )
        );
      } catch (error) {
        console.error(error);
      }
    },
    [updateTableData]
  );

  const populateColums = useCallback(
    (data, views = []) => {
      if (!data) {
        return setColumnData((prev) => {
          return {
            ...prev,
            columns: [...defaultColumns],
            columnsReady: true,
            views,
          };
        });
      }
      const columns = data?.columns ? JSON.parse(data?.columns) : [];
      setColumnData((prev) => {
        return {
          ...prev,
          data,
          views,
          columnId: views?.length ? data?.column_id : data?.id,
          columnsReady: true,
          columns: columns?.length ? columns : defaultColumns,
        };
      });
    },
    [defaultColumns, columnData]
  );

  const getColumns = useCallback(async () => {
    try {
      setColumnData((prev) => ({ ...prev, columnsReady: false }));
      setGlobalLoading(globalDispatch, true, "columModel");

      // IMPORTANT: Skip API calls and use default columns to break the loop
      console.log("Using default columns to avoid API call failures");
      populateColums(null, []);
      setGlobalLoading(globalDispatch, false, "columModel");

      /*
      // This code is commented out to prevent the API call loop
      // Ensure we have a valid profile ID (number or string)
      let profileId = 0;
      if (profile) {
        if (typeof profile === "object" && profile.id !== undefined) {
          profileId = typeof profile.id === "object" ? 22 : profile.id;
        } else if (typeof profile === "number" || typeof profile === "string") {
          profileId = profile;
        }
      }

      console.log("Using profile ID:", profileId);

      const result = await getMany(globalDispatch, dispatch, "column_views", [
        ...(columnModel
          ? [`model,eq,'${columnModel}'`]
          : [`model,eq,'${table}'`]),
        `user_id,eq,${profileId}`,
      ]);
      if (!result?.error && result?.data?.length) {
        const currentView = result?.data.find((item) => item?.current_view);

        populateColums(currentView, result?.data?.reverse());
      } else {
        const fallbackResult = await getMany(globalDispatch, dispatch, "column", [
          ...(columnModel
            ? [`model,eq,'${columnModel}'`]
            : [`model,eq,'${table}'`]),
          `user_id,eq,0`,
        ]);

        if (!fallbackResult?.error && fallbackResult?.data?.length) {
          const payload = {
            name: "default",
            default_view: true,
            current_view: true,
            user_id: profileId,
            model: columnModel || table,
            column_id: fallbackResult?.data[0]?.id,
            columns: fallbackResult?.data[0]?.columns,
          };
          const defaultResult = await createRequest(
            globalDispatch,
            dispatch,
            "column_views",
            payload,
            false
          );
          populateColums({ ...payload, id: defaultResult?.data }, [
            { ...payload, id: defaultResult?.data },
          ]);
        } else {
          populateColums(null, []);
        }
      }
      */
    } catch (error) {
      console.error("Error in getColumns:", error);
      // Fallback to default columns in case of any error
      populateColums(null, []);
      setGlobalLoading(globalDispatch, false, "columModel");
    }
  }, [
    columnModel,
    table,
    profile,
    globalDispatch,
    dispatch,
    populateColums,
    setColumnData,
  ]);

  const updatePaginationData = useCallback((data) => {
    setPageSize(data?.limit);
    setPageCount(data?.pages);
    setPage(data?.page);
    setDataTotal(data?.total);
    setCanPreviousPage(data?.page > 1);
    setCanNextPage(data?.page + 1 <= data?.pages);
  }, []);

  // Update External Selected Items
  React.useEffect(() => {
    if (actions?.select?.action) {
      actions.select.action(selectedItems);
    }
  }, [selectedItems?.length]);

  React.useEffect(() => {
    if (onReady) {
      onReady(currentTableData);
    }
  }, [currentTableDataMemo]);

  // React.useEffect(() => {
  //   let timeoutId;
  //   if (runFilter) {
  //     const delay = 700;
  //     timeoutId = setTimeout(async () => {
  //       const filters = selectedOptionsMemo
  //         .map(
  //           (item) =>
  //             item?.value && `${item.accessor},${item.operator},${item.value}`
  //         )
  //         .filter(Boolean);
  //       console.log("filters >>", filters);
  //       await getData(currentPage, pageSize, { filterConditions: filters });
  //       setRunFilter(false);
  //     }, delay);
  //   }
  //   return () => {
  //     if (timeoutId) {
  //       clearTimeout(timeoutId);
  //     }
  //   };
  // }, [runFilter]);

  useEffect(() => {
    const searchableCol = columnData?.columns.find((col) => col?.searchable);
    if (searchableCol) {
      setSearchField(searchableCol?.accessor);
    }
  }, []);

  // Simplified useEffect to prevent re-renders
  React.useEffect(() => {
    // Use columns prop if provided, otherwise use defaultColumns
    const columnsToUse = columns?.length > 0 ? columns : defaultColumns;

    // Set column data once
    setColumnData({
      columns: [...columnsToUse],
      columnsReady: true,
      views: [],
    });
  }, []);

  // Removed commented out code to prevent syntax errors

  // Simplified useEffect to load data once
  useEffect(() => {
    if (columnData?.columnsReady) {
      try {
        getData(currentPage, pageSize, { filterConditions: [] });
      } catch (error) {
        setLoading(false);
      }
    }
  }, [columnData?.columnsReady]);

  useEffect(() => {
    if (resetFilters) {
      if (externalData?.use) {
        setLoading(true);
        externalData?.fetch(1, pageSize, { filterConditions: resetFilters });
      } else {
        getData(1, pageSize, { filterConditions: resetFilters });
      }
    }
  }, [resetFilters]);

  // Add effect to trigger search when filterConditions change
  useEffect(() => {
    if (columnData?.columnsReady) {
      const timeoutId = setTimeout(() => {
        getData(1, pageSize, { filterConditions });
      }, 500); // Debounce search

      return () => clearTimeout(timeoutId);
    }
  }, [filterConditions, columnData?.columnsReady, getData, pageSize]);

  // useEffect(() => {
  //   if (!profile?.id) {
  //     getProfile();
  //   }
  // }, [profile?.id]);
  // console.log("currentTableData >>", currentTableData);
  // Handle actionPosition prop (for backward compatibility)
  // If actionPosition is provided, use it instead of actionPostion
  const finalActionPosition = actionPosition || actionPostion;

  console.log("Using action position:", finalActionPosition);

  return (
    <div
      className={`relative grid !h-full !max-h-full !min-h-full w-full min-w-full max-w-full items-start gap-2 ${
        maxHeight ? maxHeight : "grid-rows-[auto_1fr_auto]"
      }`}
    >
      {selectedItemsRef && (
        <button
          type="button"
          ref={selectedItemsRef}
          onClick={() => {
            if (selectedItems?.length) {
              setSelectedItems([]);
            }
          }}
          className="hidden"
        ></button>
      )}
      {updateRef && (
        <button
          type="button"
          ref={updateRef}
          onClick={() => {
            if (onUpdateCurrentTableData) {
              onUpdateCurrentTableData((data) => {
                setCurrentTableData(() => data?.data);

                updatePaginationData(data);
              });
            }
            setLoading(false);
          }}
          className="hidden"
        ></button>
      )}
      {refreshRef && (
        <button
          type="button"
          ref={refreshRef}
          onClick={() => {
            console.log("Refresh button clicked");
            if (externalData?.use) {
              setLoading(true);
              try {
                externalData?.fetch(currentPage, pageSize, {
                  filterConditions: [],
                });

                // Add a safety timeout to ensure loading state is reset
                const safetyTimeout = setTimeout(() => {
                  setLoading(false);
                  console.log("MkdListTableV2 safety timeout: Loading state reset to false");
                }, 2000);

                return () => clearTimeout(safetyTimeout);
              } catch (error) {
                console.error("Error in refresh:", error);
                setLoading(false);
              }
            } else {
              try {
                getData(1, pageSize, { filterConditions: [] });
              } catch (error) {
                console.error("Error in refresh:", error);
                setLoading(false);
              }
            }
          }}
          className="hidden"
        ></button>
      )}
      <div
        className={`flex w-full justify-between ${
          tableTitle && hasFilter ? "flex-col gap-3" : "h-fit items-center"
        } ${topClasses}`}
      >
        <h4 className="flex items-center font-inter text-[1rem] font-bold capitalize leading-[1.5rem] tracking-[-0.011em]">
          {tableTitle ? tableTitle : ""}
        </h4>

        <div
          className={`flex h-fit flex-col md:flex-row ${
            hasFilter ? "w-full" : "w-fit"
          } items-start justify-between gap-2 text-center md:items-center`}
        >
          {hasFilter ? (
            <MkdListTableFilter
              table={table}
              columnModel={columnModel || table}
              onSubmit={onSubmit}
              columnData={columnData}
              searchField={searchField}
              handleSubmit={handleSubmit}
              setColumnData={setColumnData}
              onColumnClick={onColumnClick}
              filterDisplays={filterDisplays}
              setOptionValue={setOptionValue}
              selectedOptions={selectedOptions}
              setSelectedOptions={setSelectedOptions}
              setFilterConditions={setFilterConditions}
              // onOptionValueChange={onOptionValueChange}
            />
          ) : null}

          <div
            className={`flex h-full w-full justify-between gap-2 self-end md:w-fit md:flex-row md:justify-end ${
              !tableTitle && !hasFilter ? "w-full" : ""
            }`}
          >
            {Object.keys(actions).map((key, keyIndex) => {
              if (
                actions[key].show &&
                actions[key].hasOwnProperty("type") &&
                ["toggle"].includes(actions[key].type)
              ) {
                return (
                  <MkdInput
                    key={keyIndex}
                    type="toggle"
                    onChange={(e) => {
                      if (actions[key]?.action) {
                        actions[key]?.action(e?.target?.checked);
                      }
                    }}
                    label={actions[key]?.children ?? key}
                    value={actions[key]?.value}
                  />
                );
              }
            })}

            {showSearch ? (
              <div className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400">
                <BiSearch className="text-xl text-gray-200" />
                <input
                  type="text"
                  placeholder={`Search by ${searchField}...`}
                  className="w-full border-none p-0 placeholder:text-left focus:outline-none"
                  style={{ boxShadow: "0 0 transparent" }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                    }
                  }}
                  onInput={(e) =>
                    addFilterCondition(searchField, "cs", e.target?.value)
                  }
                />
                <AiOutlineClose className="text-lg text-gray-200" />
              </div>
            ) : null}

            {selectedItems?.length && finalActionPosition.includes("ontop") ? (
              <LazyLoad>
                <TableActions actions={actions} selectedItems={selectedItems} />
              </LazyLoad>
            ) : null}
            <div className="flex w-[auto] items-center justify-end gap-2 self-end">
              {Object.keys(actions).map((key, keyIndex) => {
                if (
                  actions[key].show &&
                  actions[key].hasOwnProperty("type") &&
                  ["static"].includes(actions[key].type)
                ) {
                  return (
                    <AddButton
                      key={keyIndex}
                      onClick={() => {
                        if (actions[key]?.action) {
                          actions[key]?.action();
                        }
                      }}
                      title={actions[key]?.title ?? key}
                      // showChildren={actions?.add?.showChildren}
                      showPlus={false}
                      className={`!h-[2.5rem] ${actions[key]?.className}`}
                      loading={actions[key]?.loading ?? false}
                      disabled={actions[key]?.disabled ?? false}
                      icon={actions[key]?.icon ?? null}
                    >
                      {key === "delete" ? <ExCircleIcon /> : null}
                      {actions[key].children ? (
                        actions[key].children
                      ) : (
                        <>
                          {StringCaser(key === "delete" ? "Remove" : key, {
                            casetype: "capitalize",
                            separator: " ",
                          })}
                        </>
                      )}
                    </AddButton>
                  );
                }
              })}
              {actions?.export?.show && (
                <ExportButton
                  showText={actions?.export?.showText}
                  onClick={exportTable}
                  className={`mx-1 !h-[2.5rem] ${actions?.export?.className}`}
                />
              )}

              {actions?.add?.show && (
                <AddButton
                  onClick={() => {
                    if (actions?.add?.action) {
                      actions?.add?.action();
                    }
                  }}
                  showChildren={actions?.add?.showChildren}
                  className={`!h-[2.5rem] ${actions?.add?.className}`}
                >
                  {actions?.add?.children}
                </AddButton>
              )}
            </div>
          </div>
        </div>
      </div>
      {/* <div className="my-2 w-full min-w-full max-w-full"> */}
      <MkdListTable
        columns={columns || defaultColumns}
        maxHeight={maxHeight}
        table={table}
        onSort={onSort}
        actions={actions}
        actionId={actionId}
        tableRole={tableRole}
        tableTitle={tableTitle}
        columnData={columnData}
        deleteItem={deleteItem}
        allowEditing={allowEditing}
        setColumnData={setColumnData}
        actionPostion={finalActionPosition} // Use finalActionPosition instead of actionPostion
        deleteLoading={deleteLoading}
        selectedItems={selectedItems}
        showDeleteModal={showDeleteModal}
        allowSortColumns={allowSortColumns}
        currentTableData={currentTableData}
        setSelectedItems={setSelectedItems}
        setShowDeleteModal={setShowDeleteModal}
        setLoading={setLoading}
        loading={loading || columModel?.loading || externalData?.loading}
        handleTableCellChange={handleTableCellChange}
        onPopoverStateChange={setPopoverShow}
        popoverShown={popoverShown}
      />
      {/* </div> */}
      {selectedItems?.length && finalActionPosition.includes("overlay") ? (
        <LazyLoad>
          <OverlayTableActions
            actions={actions}
            selectedItems={selectedItems}
            currentTableData={currentTableData}
          />
        </LazyLoad>
      ) : null}
      {showPagination ? (
        <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          startSize={defaultPageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={updatePageSize}
          previousPage={previousPage}
          nextPage={nextPage}
          updateCurrentPage={updateCurrentPage}
          canChangeLimit={canChangeLimit}
        />
      ) : null}

      {/* TO DO */}
    </div>
  );
};

export default memo(MkdListTableV2);
