import React, { useRef } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { LazyLoad } from "Components/LazyLoad";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV2 } from "Components/MkdListTable";
import {
  AdminEditCommunityPage,
  AdminAddCommunityPage,
} from "Src/routes/LazyLoad";
import "./AdminListCommunityPage.css";
import { AiFillEye } from "react-icons/ai";
import { EditIcon2, TrashIcon } from "Assets/svgs";

const columns = [
  {
    header: "ID",
    accessor: "id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Community Name",
    accessor: "title",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Created By",
    accessor: "user_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value, row) => {
      try {
        if (row.user_details) {
          const userDetails = JSON.parse(row.user_details);
          return `${userDetails.first_name} ${userDetails.last_name}`;
        }
      } catch (e) {
        console.error("Error parsing user details", e);
      }
      return `User ${value}`;
    }
  },
  {
    header: "Admins",
    accessor: "admin_count",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    // Since this isn't in the API response, we'll use a placeholder
    cellRenderer: () => {
      return Math.floor(Math.random() * 5) + 1; // Random number between 1-5 for demo
    }
  },
  {
    header: "Moderators",
    accessor: "moderator_count",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    // Since this isn't in the API response, we'll use a placeholder
    cellRenderer: () => {
      return Math.floor(Math.random() * 8) + 1; // Random number between 1-8 for demo
    }
  },
  {
    header: "Monthly Fee",
    accessor: "subscription_fee",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Simply use the subscription_fee field directly
      return parseFloat(value) > 0 ? `$${parseFloat(value).toFixed(2)}` : "Free";
    }
  },
  {
    header: "Affiliate",
    accessor: "privacy_settings",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      try {
        if (value) {
          // Parse the privacy_settings JSON string
          const settings = JSON.parse(value);

          // Check if enable_affiliate is true
          const isAffiliate = settings.enable_affiliate === true;

          // Return a styled Yes/No based on the enable_affiliate value only
          return isAffiliate ?
            '<span class="yes-text">Yes</span>' :
            '<span class="no-text">No</span>';
        }
      } catch (e) {
        console.error("Error parsing privacy settings", e);
      }
      // Default to No if there's an error or no privacy_settings
      return '<span class="no-text">No</span>';
    }
  },

];

const AdminCommunityListPage = () => {
  const navigate = useNavigate();
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const [searchValue, setSearchValue] = React.useState("");
  const refreshRef = useRef(null);
  const [stats] = React.useState({
    total: 156,
    public: 89,
    private: 67
  });

  // State to manage table data and loading
  const [tableData, setTableData] = React.useState({
    data: [
      {
        id: 56,
        title: "test_test",
        description: "asdfasfd",
        description_image: null,
        logo: null,
        industry_id: 2,
        has_paid: 1,
        guidelines: "<p>asdfasddf</p>",
        metadata: null,
        privacy: "private",
        user_id: 2,
        user_details: "{\"first_name\":\"Ad\",\"last_name\":\"Smith\",\"linkedin_profile\":\"\",\"website_url\":\"\",\"additional_info\":\"\"}",
        created_at: "2025-05-01T13:37:59.000Z",
        updated_at: "2025-05-01T13:37:59.000Z",
        privacy_settings: "{\"who_can_invite\":\"everyone\",\"activity_visibility\":\"public\",\"who_can_post\":\"everyone\",\"view_list\":\"members_only\",\"who_can_find\":\"hidden\",\"who_can_join\":\"invite_only\",\"who_can_see_content\":\"members_only\",\"content_moderation\":\"admin_approval\",\"enable_affiliate\":false,\"subscription_fee\":\"01234\"}",
        subscription_fee: "1234.00"
      },
      {
        id: 59,
        title: "Deal Maker LLC",
        description: "A community for deal makers",
        description_image: null,
        logo: null,
        industry_id: 3,
        has_paid: 1,
        guidelines: "<p>Follow the rules</p>",
        metadata: null,
        privacy: "private",
        user_id: 28,
        user_details: "{\"first_name\":\"John\",\"last_name\":\"Doe\",\"linkedin_profile\":\"\",\"website_url\":\"\",\"additional_info\":\"\"}",
        created_at: "2025-05-05T14:22:18.000Z",
        updated_at: "2025-05-05T14:22:18.000Z",
        privacy_settings: "{\"who_can_invite\":\"everyone\",\"activity_visibility\":\"public\",\"who_can_post\":\"everyone\",\"view_list\":\"everyone\",\"who_can_find\":\"everyone\",\"who_can_join\":\"anyone\",\"who_can_see_content\":\"everyone\",\"content_moderation\":\"admin_approval\",\"enable_affiliate\":true,\"subscription_fee\":\"99.99\"}",
        subscription_fee: "99.99"
      }
    ],
    loading: false,
    page: 1,
    limit: 10,
    pages: 16,
    total: 156
  });

  const onToggleModal = async (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setActiveEditId(ids[0]);
        break;
      case "delete":
        await handleDelete(ids);
        break;
      default:
        break;
    }
  };

  // Function to handle delete action
  const handleDelete = async (ids) => {
    try {
      console.log("Deleting community with ID:", ids[0]);

      // Set loading state to true
      setTableData(prev => ({...prev, loading: true}));

      const sdk = new MkdSDK();
      sdk.setTable("community");
      const result = await sdk.callRestAPI({ id: ids[0] }, "DELETE");

      console.log("Delete result:", result);

      if (!result?.error) {
        // Update the table data by removing the deleted item
        setTableData(prev => ({
          ...prev,
          data: prev.data.filter(item => item.id !== ids[0]),
          loading: false
        }));

        // No need to click refresh button as we've already updated the data
      } else {
        // If there was an error, just set loading to false
        setTableData(prev => ({...prev, loading: false}));
      }
    } catch (error) {
      console.error("Error deleting community:", error);
      // Make sure to set loading to false in case of error
      setTableData(prev => ({...prev, loading: false}));
    }
  };

  return (
    <>
      <div className="communities-dashboard bg-[#1E1E1E]">
        <div className="container">
          {/* Header */}
          <div className="header">
            <h1>Communities Dashboard</h1>
            <p>Monitor, manage, and track community engagement</p>
          </div>

          {/* Search and Add Button */}
          <div className="search-add">
            <div className="search-container">
              <div className="search-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search communities..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="search-input"
              />
            </div>
            <button
              onClick={() => onToggleModal("add", true)}
              className="add-button"
            >
              <span>+</span> Add Community
            </button>
          </div>

          {/* Stats Cards */}
          

          {/* Table */}
          <div className="table-container">
            <LazyLoad>
              <MkdListTableV2
                columns={columns}
                tableRole={"admin"}
                table={"community"}
                actionId={"id"}
                actions={{
                  view: {
                    show: true,
                    action: (ids) => {
                      navigate(`/admin/view-community/${ids[0]}`);
                    },
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "View",
                    icon: <AiFillEye className="text-blue-500" />
                  },
                  edit: {
                    show: true,
                    multiple: false,
                    action: (ids) => onToggleModal("edit", true, ids),
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Edit",
                    icon: <EditIcon2 stroke="#4CAF50" />
                  },
                  delete: {
                    show: true,
                    action: handleDelete,
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Delete",
                    icon: <TrashIcon fill="#E53E3E" />
                  },
                  select: { show: false, action: null, multiple: false },
                  add: {
                    show: true,
                    action: () => onToggleModal("add", true),
                    multiple: false,
                    children: "Add Community",
                    showChildren: true,
                  },
                  export: { show: false, action: null, multiple: true },
                }}
                actionPosition={["buttons"]}
                refreshRef={refreshRef}
                // Sample data based on the actual API response structure
                externalData={{
                  use: true,
                  data: tableData.data,
                  loading: tableData.loading,
                  page: tableData.page,
                  limit: tableData.limit,
                  pages: tableData.pages,
                  total: tableData.total,
                  fetch: (page, limit, filter) => {
                    // This function is called when the table needs to refresh
                    console.log("Fetch called with page:", page, "limit:", limit, "filter:", filter);
                    // We're using static data, so just set loading to false after a short delay
                    // In a real app, you would make an API call here
                    setTimeout(() => {
                      setTableData(prev => ({...prev, loading: false}));
                    }, 500);
                  },
                  search: (search, _columns, _searchFilter, query) => {
                    // This function is called when the search is triggered
                    console.log("Search called with:", search, query);
                    // We're using static data, so just set loading to false after a short delay
                    setTimeout(() => {
                      setTableData(prev => ({...prev, loading: false}));
                    }, 500);
                  }
                }}
              />
            </LazyLoad>
          </div>

          {/* Pagination */}
          <div className="pagination">
            <div className="pagination-info">Showing 1-2 of 156 communities</div>
            <div className="pagination-buttons">
              <button className="pagination-button prev">Previous</button>
              <button className="pagination-button next">Next</button>
            </div>
          </div>
        </div>
      </div>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showAddSidebar}
          closeModalFn={() => setShowAddSidebar(false)}
        >
          <AdminAddCommunityPage setSidebar={setShowAddSidebar} />
        </ModalSidebar>
      </LazyLoad>

      {showEditSidebar && (
        <LazyLoad>
          <ModalSidebar
            isModalActive={showEditSidebar}
            closeModalFn={() => setShowEditSidebar(false)}
          >
            <AdminEditCommunityPage
              activeId={activeEditId}
              setSidebar={setShowEditSidebar}
            />
          </ModalSidebar>
        </LazyLoad>
      )}
    </>
  );
};

export default AdminCommunityListPage;
